"""
Enhanced SQL splitter with improved position tracking and error mapping support.
"""

import re
from typing import List, <PERSON><PERSON>, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class StatementInfo:
    """Information about a split statement including position tracking."""
    content: str
    original_start_pos: int
    original_end_pos: int
    original_start_line: int
    original_end_line: int
    statement_number: int
    is_merged: bool = False
    merge_source_statements: List[int] = None


class EnhancedSQLSplitter:
    """Enhanced SQL splitter with comprehensive position tracking."""
    
    def __init__(self):
        self.original_sql = ""
        self.lines = []
        self.statement_infos: List[StatementInfo] = []
        self.position_to_statement = {}
        self.line_to_statement = {}
    
    def split_with_position_tracking(self, sql_code: str) -> Tuple[List[str], List[StatementInfo]]:
        """
        Split SQL code while maintaining comprehensive position tracking.
        
        Returns:
            Tuple of (statements, statement_infos)
        """
        if not sql_code or not sql_code.strip():
            return [], []
        
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')
        self.statement_infos = []
        
        # Phase 1: Initial splitting with position tracking
        initial_statements = self._split_with_basic_tracking(sql_code)
        
        # Phase 2: PostgreSQL-specific merging with position updates
        merged_statements = self._merge_postgresql_patterns(initial_statements)
        
        # Phase 3: Create final position mappings
        self._create_position_mappings()
        
        # Extract just the statement content for backward compatibility
        statements = [stmt_info.content for stmt_info in self.statement_infos]
        
        return statements, self.statement_infos
    
    def _split_with_basic_tracking(self, sql_code: str) -> List[StatementInfo]:
        """Split SQL with basic position tracking."""
        statements = []
        current_pos = 0
        statement_num = 1
        
        # Function to check if position is within a comment
        def is_in_comment(pos: int, text: str) -> bool:
            if pos >= len(text):
                return False
            
            # Check for block comments /* */
            block_comment_pairs = []
            block_starts = [m.start() for m in re.finditer(r'/\*', text[:pos+1])]
            if block_starts:
                block_ends = [m.start() + 2 for m in re.finditer(r'\*/', text)]
                
                for start in block_starts:
                    for end in block_ends:
                        if end > start:
                            block_comment_pairs.append((start, end))
                            break
            
            # Check if position is within any block comment
            for start, end in block_comment_pairs:
                if start < pos < end:
                    return True
            
            # Check for line comments --
            line_starts = [m.start() for m in re.finditer(r'--', text[:pos+1])]
            for start in line_starts:
                line_end = text.find('\n', start)
                if line_end == -1:
                    line_end = len(text)
                if start < pos < line_end:
                    return True
            
            return False
        
        # Function to check if position is within quotes
        def is_in_quotes(pos: int, text: str) -> bool:
            if pos >= len(text):
                return False
            
            quote_positions = [m.start() for m in re.finditer(r"'", text[:pos])]
            
            # Handle escaped quotes
            i = 0
            while i < len(quote_positions) - 1:
                if quote_positions[i+1] - quote_positions[i] == 1:
                    quote_positions.pop(i)
                    quote_positions.pop(i)
                else:
                    i += 1
            
            return len(quote_positions) % 2 == 1
        
        remaining_code = sql_code.strip()
        
        # First, try to split on AS or IS keywords
        as_is_pattern = r'\b(AS|IS)\b'
        match = re.search(as_is_pattern, remaining_code, re.IGNORECASE)
        
        if match and not is_in_comment(match.start(), remaining_code) and not is_in_quotes(match.start(), remaining_code):
            # Split before the match
            first_part = remaining_code[:match.start()].strip()
            if first_part:
                start_line = self._get_line_number(current_pos)
                end_pos = current_pos + match.start() - 1
                end_line = self._get_line_number(end_pos)
                
                stmt_info = StatementInfo(
                    content=first_part,
                    original_start_pos=current_pos,
                    original_end_pos=end_pos,
                    original_start_line=start_line,
                    original_end_line=end_line,
                    statement_number=statement_num
                )
                statements.append(stmt_info)
                statement_num += 1
            
            # Update position and remaining code
            current_pos += match.start()
            remaining_code = remaining_code[match.start():].strip()
        
        # Split by semicolons
        if remaining_code:
            semicolon_positions = [m.start() for m in re.finditer(';', remaining_code)]
            
            if not semicolon_positions:
                # No semicolons found
                if remaining_code.strip():
                    start_line = self._get_line_number(current_pos)
                    end_pos = current_pos + len(remaining_code) - 1
                    end_line = self._get_line_number(end_pos)
                    
                    stmt_info = StatementInfo(
                        content=remaining_code.strip(),
                        original_start_pos=current_pos,
                        original_end_pos=end_pos,
                        original_start_line=start_line,
                        original_end_line=end_line,
                        statement_number=statement_num
                    )
                    statements.append(stmt_info)
            else:
                last_pos = 0
                for pos in semicolon_positions:
                    if not is_in_comment(pos, remaining_code) and not is_in_quotes(pos, remaining_code):
                        stmt_content = remaining_code[last_pos:pos+1].strip()
                        if stmt_content:
                            abs_start_pos = current_pos + last_pos
                            abs_end_pos = current_pos + pos
                            start_line = self._get_line_number(abs_start_pos)
                            end_line = self._get_line_number(abs_end_pos)
                            
                            stmt_info = StatementInfo(
                                content=stmt_content,
                                original_start_pos=abs_start_pos,
                                original_end_pos=abs_end_pos,
                                original_start_line=start_line,
                                original_end_line=end_line,
                                statement_number=statement_num
                            )
                            statements.append(stmt_info)
                            statement_num += 1
                        last_pos = pos + 1
                
                # Add remaining code after last semicolon
                if last_pos < len(remaining_code):
                    remaining_part = remaining_code[last_pos:].strip()
                    if remaining_part:
                        abs_start_pos = current_pos + last_pos
                        abs_end_pos = current_pos + len(remaining_code) - 1
                        start_line = self._get_line_number(abs_start_pos)
                        end_line = self._get_line_number(abs_end_pos)
                        
                        stmt_info = StatementInfo(
                            content=remaining_part,
                            original_start_pos=abs_start_pos,
                            original_end_pos=abs_end_pos,
                            original_start_line=start_line,
                            original_end_line=end_line,
                            statement_number=statement_num
                        )
                        statements.append(stmt_info)
        
        return statements
    
    def _get_line_number(self, char_pos: int) -> int:
        """Get line number for a character position."""
        if char_pos >= len(self.original_sql):
            return len(self.lines)
        
        lines_before = self.original_sql[:char_pos].count('\n')
        return lines_before + 1
    
    def _merge_postgresql_patterns(self, statements: List[StatementInfo]) -> List[StatementInfo]:
        """Merge PostgreSQL-specific patterns while updating position tracking."""
        if not statements:
            return statements
        
        merged = []
        i = 0
        
        while i < len(statements):
            current_stmt = statements[i]
            
            # Check if current statement ends with 'end;' pattern
            if self._is_postgresql_end_pattern(current_stmt.content):
                # Look ahead for dollar-quoted string
                merged_stmt = current_stmt
                j = i + 1
                
                while j < len(statements):
                    next_stmt = statements[j]
                    
                    if self._is_dollar_quoted_pattern(next_stmt.content):
                        # Merge statements
                        merged_content = merged_stmt.content + next_stmt.content
                        merged_stmt = StatementInfo(
                            content=merged_content,
                            original_start_pos=merged_stmt.original_start_pos,
                            original_end_pos=next_stmt.original_end_pos,
                            original_start_line=merged_stmt.original_start_line,
                            original_end_line=next_stmt.original_end_line,
                            statement_number=merged_stmt.statement_number,
                            is_merged=True,
                            merge_source_statements=[merged_stmt.statement_number, next_stmt.statement_number]
                        )
                        i = j + 1
                        break
                    elif next_stmt.content.strip() == "":
                        j += 1
                    else:
                        i += 1
                        break
                else:
                    i += 1
                
                merged.append(merged_stmt)
            else:
                merged.append(current_stmt)
                i += 1
        
        return merged
    
    def _is_postgresql_end_pattern(self, statement: str) -> bool:
        """Check if statement matches PostgreSQL 'end;' pattern."""
        if not statement:
            return False
        
        stmt = statement.strip()
        pattern = r'\bend\s*(?:\w+\s*)?\s*;$'
        return bool(re.search(pattern, stmt, re.IGNORECASE))
    
    def _is_dollar_quoted_pattern(self, statement: str) -> bool:
        """Check if statement is a dollar-quoted pattern."""
        if not statement:
            return False
        
        stmt = statement.strip()
        pattern = r'^\$\w*\$\s*;?\s*$'
        return bool(re.match(pattern, stmt))
    
    def _create_position_mappings(self):
        """Create comprehensive position mappings."""
        self.position_to_statement = {}
        self.line_to_statement = {}
        
        for stmt_info in self.statement_infos:
            # Map character positions to statement
            for pos in range(stmt_info.original_start_pos, stmt_info.original_end_pos + 1):
                self.position_to_statement[pos] = stmt_info.statement_number
            
            # Map lines to statement
            for line in range(stmt_info.original_start_line, stmt_info.original_end_line + 1):
                self.line_to_statement[line] = stmt_info.statement_number
    
    def get_statement_by_position(self, char_pos: int) -> Optional[StatementInfo]:
        """Get statement info by character position."""
        stmt_num = self.position_to_statement.get(char_pos)
        if stmt_num:
            return next((stmt for stmt in self.statement_infos if stmt.statement_number == stmt_num), None)
        return None
    
    def get_statement_by_line(self, line_num: int) -> Optional[StatementInfo]:
        """Get statement info by line number."""
        stmt_num = self.line_to_statement.get(line_num)
        if stmt_num:
            return next((stmt for stmt in self.statement_infos if stmt.statement_number == stmt_num), None)
        return None


# Backward compatibility function
def split_sql_statements_enhanced(sql_code: str) -> Tuple[List[str], List[StatementInfo]]:
    """Enhanced version of split_sql_statements with position tracking."""
    splitter = EnhancedSQLSplitter()
    return splitter.split_with_position_tracking(sql_code)
