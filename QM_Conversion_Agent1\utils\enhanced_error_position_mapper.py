"""
Enhanced position-based error mapping utilities with improved accuracy and robustness.
"""

import re
from typing import List, Tuple, Optional, Dict, Any
from formatting.enhanced_sql_splitter import EnhancedS<PERSON>Splitter, StatementInfo


class UniversalErrorExtractor:
    """Extract position information from any database error message with enhanced patterns."""
    
    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """Extract line or position information from error message with enhanced patterns."""
        position_info = {}
        
        # PostgreSQL line format: "LINE 139:" or "line 139:"
        line_match = re.search(r'LINE\s+(\d+):', error_message, re.IGNORECASE)
        if line_match:
            position_info['line'] = int(line_match.group(1))
            return position_info
        
        # Position format: "Position: 17013" or "position 15420"
        pos_match = re.search(r'position[:\s]+(\d+)', error_message, re.IGNORECASE)
        if pos_match:
            position_info['position'] = int(pos_match.group(1))
            return position_info
        
        # Line format: "line: 447" or "line 184"
        line_match2 = re.search(r'line[:\s]+(\d+)', error_message, re.IGNORECASE)
        if line_match2:
            position_info['line'] = int(line_match2.group(1))
            return position_info
        
        # Oracle format: "ORA-00933: SQL command not properly ended at line 15"
        oracle_line_match = re.search(r'at\s+line\s+(\d+)', error_message, re.IGNORECASE)
        if oracle_line_match:
            position_info['line'] = int(oracle_line_match.group(1))
            return position_info
        
        # MySQL format: "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'FROM' at line 5"
        mysql_line_match = re.search(r'at\s+line\s+(\d+)', error_message, re.IGNORECASE)
        if mysql_line_match:
            position_info['line'] = int(mysql_line_match.group(1))
            return position_info
        
        # Generic character position: "at character 1234"
        char_match = re.search(r'at\s+character\s+(\d+)', error_message, re.IGNORECASE)
        if char_match:
            position_info['position'] = int(char_match.group(1))
            return position_info
        
        return position_info


class EnhancedPositionMapper:
    """Enhanced position mapping with comprehensive statement tracking and error recovery."""
    
    def __init__(self):
        self.splitter = EnhancedSQLSplitter()
        self.statement_infos: List[StatementInfo] = []
        self.original_sql = ""
        self.validation_errors = []
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'EnhancedPositionMapper']:
        """Split SQL and create comprehensive position mapping with validation."""
        try:
            self.original_sql = sql_code
            statements, self.statement_infos = self.splitter.split_with_position_tracking(sql_code)
            
            # Validate the mapping
            self._validate_position_mapping()
            
            return statements, self
        except Exception as e:
            self.validation_errors.append(f"Mapping creation failed: {str(e)}")
            return [], self
    
    def _validate_position_mapping(self):
        """Validate that position mapping is accurate."""
        validation_errors = []
        
        for stmt_info in self.statement_infos:
            # Check if the statement content can be found at the reported position
            try:
                actual_content = self.original_sql[stmt_info.original_start_pos:stmt_info.original_end_pos + 1]
                
                # Normalize whitespace for comparison
                normalized_actual = re.sub(r'\s+', ' ', actual_content.strip())
                normalized_expected = re.sub(r'\s+', ' ', stmt_info.content.strip())
                
                if normalized_actual != normalized_expected:
                    # Try fuzzy matching
                    similarity = self._calculate_similarity(normalized_actual, normalized_expected)
                    if similarity < 0.8:  # 80% similarity threshold
                        validation_errors.append(
                            f"Statement {stmt_info.statement_number}: Position mismatch. "
                            f"Expected: '{normalized_expected[:50]}...', "
                            f"Found: '{normalized_actual[:50]}...', "
                            f"Similarity: {similarity:.2f}"
                        )
            except IndexError:
                validation_errors.append(
                    f"Statement {stmt_info.statement_number}: Position out of bounds "
                    f"({stmt_info.original_start_pos}-{stmt_info.original_end_pos})"
                )
        
        self.validation_errors = validation_errors
        
        if validation_errors:
            print(f"⚠️ Position mapping validation found {len(validation_errors)} issues:")
            for error in validation_errors[:3]:  # Show first 3 errors
                print(f"   - {error}")
            if len(validation_errors) > 3:
                print(f"   ... and {len(validation_errors) - 3} more issues")
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings using simple ratio."""
        if not str1 and not str2:
            return 1.0
        if not str1 or not str2:
            return 0.0
        
        # Simple character-based similarity
        max_len = max(len(str1), len(str2))
        min_len = min(len(str1), len(str2))
        
        # Count matching characters at the beginning
        matching_chars = 0
        for i in range(min_len):
            if str1[i] == str2[i]:
                matching_chars += 1
            else:
                break
        
        return matching_chars / max_len
    
    def has_valid_position_mapping(self) -> bool:
        """Check if position mapping was successfully created and is valid."""
        return (len(self.statement_infos) > 0 and 
                len(self.validation_errors) < len(self.statement_infos) * 0.5)  # Allow up to 50% errors
    
    def find_statement_by_position(self, position_info: Dict[str, int]) -> List[Tuple[StatementInfo, str, int]]:
        """Find statement by line or character position with enhanced accuracy."""
        candidates = []
        
        if 'line' in position_info:
            line_num = position_info['line']
            stmt_info = self.splitter.get_statement_by_line(line_num)
            if stmt_info:
                candidates.append((stmt_info, 'line', line_num))
        
        if 'position' in position_info:
            char_pos = position_info['position']
            stmt_info = self.splitter.get_statement_by_position(char_pos)
            if stmt_info:
                candidates.append((stmt_info, 'position', char_pos))
        
        return candidates
    
    def find_statement_by_content_fuzzy(self, error_snippet: str, max_results: int = 3) -> List[Tuple[StatementInfo, float]]:
        """Find statements by content using fuzzy matching."""
        if not error_snippet or len(error_snippet.strip()) < 10:
            return []
        
        candidates = []
        error_snippet_normalized = re.sub(r'\s+', ' ', error_snippet.strip().lower())
        
        for stmt_info in self.statement_infos:
            stmt_normalized = re.sub(r'\s+', ' ', stmt_info.content.strip().lower())
            
            # Check if error snippet is contained in statement
            if error_snippet_normalized in stmt_normalized:
                similarity = 1.0
                candidates.append((stmt_info, similarity))
            else:
                # Calculate similarity
                similarity = self._calculate_similarity(error_snippet_normalized, stmt_normalized)
                if similarity > 0.3:  # 30% similarity threshold
                    candidates.append((stmt_info, similarity))
        
        # Sort by similarity and return top results
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:max_results]
    
    def get_duplicate_statements(self, statement_info: StatementInfo) -> List[StatementInfo]:
        """Get all statements with similar content (duplicates)."""
        duplicates = []
        target_content = re.sub(r'\s+', ' ', statement_info.content.strip().lower())
        
        for stmt_info in self.statement_infos:
            if stmt_info.statement_number == statement_info.statement_number:
                duplicates.append(stmt_info)
                continue
            
            stmt_content = re.sub(r'\s+', ' ', stmt_info.content.strip().lower())
            similarity = self._calculate_similarity(target_content, stmt_content)
            
            if similarity > 0.9:  # 90% similarity for duplicates
                duplicates.append(stmt_info)
        
        return duplicates
    
    def to_dict(self) -> Dict[str, Any]:
        """Serialize to dictionary with enhanced information."""
        return {
            'statement_infos': [
                {
                    'content': stmt.content,
                    'original_start_pos': stmt.original_start_pos,
                    'original_end_pos': stmt.original_end_pos,
                    'original_start_line': stmt.original_start_line,
                    'original_end_line': stmt.original_end_line,
                    'statement_number': stmt.statement_number,
                    'is_merged': stmt.is_merged,
                    'merge_source_statements': stmt.merge_source_statements or []
                }
                for stmt in self.statement_infos
            ],
            'original_sql': self.original_sql,
            'validation_errors': self.validation_errors,
            'position_to_statement': self.splitter.position_to_statement,
            'line_to_statement': self.splitter.line_to_statement
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedPositionMapper':
        """Deserialize from dictionary with validation."""
        if not data:
            raise ValueError("Cannot deserialize from empty data")
        
        mapper = cls()
        
        try:
            # Reconstruct statement infos
            mapper.statement_infos = []
            for stmt_data in data.get('statement_infos', []):
                stmt_info = StatementInfo(
                    content=stmt_data['content'],
                    original_start_pos=stmt_data['original_start_pos'],
                    original_end_pos=stmt_data['original_end_pos'],
                    original_start_line=stmt_data['original_start_line'],
                    original_end_line=stmt_data['original_end_line'],
                    statement_number=stmt_data['statement_number'],
                    is_merged=stmt_data.get('is_merged', False),
                    merge_source_statements=stmt_data.get('merge_source_statements', [])
                )
                mapper.statement_infos.append(stmt_info)
            
            mapper.original_sql = data.get('original_sql', '')
            mapper.validation_errors = data.get('validation_errors', [])
            
            # Reconstruct splitter mappings
            mapper.splitter.position_to_statement = data.get('position_to_statement', {})
            mapper.splitter.line_to_statement = data.get('line_to_statement', {})
            mapper.splitter.statement_infos = mapper.statement_infos
            mapper.splitter.original_sql = mapper.original_sql
            
        except (KeyError, TypeError, ValueError) as e:
            raise ValueError(f"Invalid data format in enhanced position mapper: {e}")
        
        return mapper


class SmartStatementResolver:
    """Enhanced statement resolver with multiple fallback strategies."""
    
    def __init__(self, position_mapper: EnhancedPositionMapper):
        self.position_mapper = position_mapper
    
    def resolve_statement_by_position(self, error_message: str, iteration_count: int = 1) -> Optional[int]:
        """Resolve statement using multiple strategies with enhanced accuracy."""
        
        # Strategy 1: Position-based resolution
        position_result = self._resolve_by_position(error_message)
        if position_result:
            return self._handle_duplicates(position_result, iteration_count)
        
        # Strategy 2: Content-based fuzzy matching
        content_result = self._resolve_by_content(error_message)
        if content_result:
            return content_result.statement_number
        
        # Strategy 3: Pattern-based matching
        pattern_result = self._resolve_by_pattern(error_message)
        if pattern_result:
            return pattern_result.statement_number
        
        return None
    
    def _resolve_by_position(self, error_message: str) -> Optional[StatementInfo]:
        """Resolve using position information from error message."""
        if not self.position_mapper.has_valid_position_mapping():
            return None
        
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)
        
        if not position_info:
            return None
        
        candidates = self.position_mapper.find_statement_by_position(position_info)
        
        if candidates:
            return candidates[0][0]  # Return first candidate's StatementInfo
        
        return None
    
    def _resolve_by_content(self, error_message: str) -> Optional[StatementInfo]:
        """Resolve using content snippets from error message."""
        # Extract potential SQL snippets from error message
        sql_snippets = self._extract_sql_snippets(error_message)
        
        for snippet in sql_snippets:
            candidates = self.position_mapper.find_statement_by_content_fuzzy(snippet, max_results=1)
            if candidates and candidates[0][1] > 0.7:  # High similarity threshold
                return candidates[0][0]
        
        return None
    
    def _resolve_by_pattern(self, error_message: str) -> Optional[StatementInfo]:
        """Resolve using error pattern matching."""
        # Common error patterns and their indicators
        error_patterns = {
            'syntax_error': ['syntax error', 'unexpected token', 'invalid syntax'],
            'missing_from': ['missing FROM', 'FROM clause'],
            'group_by_error': ['GROUP BY', 'HAVING'],
            'function_error': ['function', 'does not exist', 'unknown function'],
            'column_error': ['column', 'does not exist', 'unknown column']
        }
        
        error_message_lower = error_message.lower()
        
        for pattern_type, keywords in error_patterns.items():
            if any(keyword.lower() in error_message_lower for keyword in keywords):
                # Find statements that might contain this pattern
                for stmt_info in self.position_mapper.statement_infos:
                    stmt_lower = stmt_info.content.lower()
                    if any(keyword.lower() in stmt_lower for keyword in keywords):
                        return stmt_info
        
        return None
    
    def _extract_sql_snippets(self, error_message: str) -> List[str]:
        """Extract potential SQL code snippets from error message."""
        snippets = []
        
        # Look for quoted SQL snippets
        quoted_matches = re.findall(r"'([^']+)'", error_message)
        snippets.extend([match for match in quoted_matches if len(match) > 5])
        
        # Look for SQL keywords that might indicate problematic code
        sql_keywords = ['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'HAVING', 'ORDER BY', 'INSERT', 'UPDATE', 'DELETE']
        for keyword in sql_keywords:
            pattern = rf'{keyword}\s+[^.]*'
            matches = re.findall(pattern, error_message, re.IGNORECASE)
            snippets.extend(matches)
        
        return snippets
    
    def _handle_duplicates(self, statement_info: StatementInfo, iteration_count: int) -> int:
        """Handle duplicate statements by rotating through them based on iteration."""
        duplicates = self.position_mapper.get_duplicate_statements(statement_info)
        
        if len(duplicates) > 1:
            # Rotate through duplicates based on iteration
            selected_index = (iteration_count - 1) % len(duplicates)
            return duplicates[selected_index].statement_number
        
        return statement_info.statement_number
