"""
Test suite for enhanced SQL splitting and error mapping functionality.
"""

import pytest
from formatting.enhanced_sql_splitter import EnhancedSQLSplitter, split_sql_statements_enhanced
from utils.enhanced_error_position_mapper import <PERSON>hancedPosition<PERSON>ap<PERSON>, SmartStatementResolver, UniversalErrorExtractor


class TestEnhancedSQLSplitter:
    """Test cases for enhanced SQL splitter."""
    
    def test_basic_splitting(self):
        """Test basic SQL statement splitting."""
        sql_code = """
        SELECT * FROM table1;
        INSERT INTO table2 VALUES (1, 'test');
        UPDATE table3 SET col1 = 'value';
        """
        
        splitter = EnhancedSQLSplitter()
        statements, statement_infos = splitter.split_with_position_tracking(sql_code)
        
        assert len(statements) == 3
        assert len(statement_infos) == 3
        
        # Check that position tracking is accurate
        for i, stmt_info in enumerate(statement_infos):
            assert stmt_info.statement_number == i + 1
            assert stmt_info.original_start_pos >= 0
            assert stmt_info.original_end_pos >= stmt_info.original_start_pos
            assert stmt_info.original_start_line >= 1
            assert stmt_info.original_end_line >= stmt_info.original_start_line
    
    def test_postgresql_merging(self):
        """Test PostgreSQL-specific pattern merging."""
        sql_code = """
        CREATE FUNCTION test_func() RETURNS void AS
        $BODY$
        BEGIN
            SELECT 1;
        END;
        $BODY$;
        
        SELECT * FROM test_table;
        """
        
        splitter = EnhancedSQLSplitter()
        statements, statement_infos = splitter.split_with_position_tracking(sql_code)
        
        # Should merge the function definition into one statement
        assert len(statements) == 2
        
        # First statement should be the merged function
        function_stmt = statements[0]
        assert '$BODY$' in function_stmt
        assert 'CREATE FUNCTION' in function_stmt
        
        # Check merge tracking
        function_info = statement_infos[0]
        assert function_info.is_merged == True
        assert function_info.merge_source_statements is not None
    
    def test_as_is_splitting(self):
        """Test AS/IS keyword splitting."""
        sql_code = """
        CREATE VIEW test_view AS
        SELECT col1, col2 FROM table1 WHERE condition = 'value';
        
        CREATE PROCEDURE test_proc IS
        BEGIN
            UPDATE table2 SET col1 = 'new_value';
        END;
        """
        
        splitter = EnhancedSQLSplitter()
        statements, statement_infos = splitter.split_with_position_tracking(sql_code)
        
        assert len(statements) == 4  # Should split on AS and IS
        
        # First statement should be before AS
        assert 'CREATE VIEW test_view' in statements[0]
        assert 'AS' not in statements[0]
        
        # Second statement should start with AS
        assert statements[1].strip().startswith('AS')
    
    def test_comment_and_quote_handling(self):
        """Test that comments and quotes are handled correctly."""
        sql_code = """
        SELECT 'test;string' FROM table1; -- This is a comment with ;
        /* Multi-line comment
           with ; semicolon */
        INSERT INTO table2 VALUES ('value;with;semicolons');
        """
        
        splitter = EnhancedSQLSplitter()
        statements, statement_infos = splitter.split_with_position_tracking(sql_code)
        
        assert len(statements) == 2
        
        # First statement should contain the quoted string
        assert "'test;string'" in statements[0]
        
        # Second statement should contain the multi-value insert
        assert "'value;with;semicolons'" in statements[1]


class TestEnhancedPositionMapper:
    """Test cases for enhanced position mapper."""
    
    def test_position_mapping_creation(self):
        """Test creation of position mappings."""
        sql_code = """
        SELECT col1, col2
        FROM table1
        WHERE condition = 'value';
        
        INSERT INTO table2 (col1, col2)
        VALUES (1, 'test');
        """
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        assert len(statements) == 2
        assert mapper.has_valid_position_mapping()
        assert len(mapper.statement_infos) == 2
        
        # Test position lookup
        first_stmt_info = mapper.splitter.get_statement_by_line(1)
        assert first_stmt_info is not None
        assert first_stmt_info.statement_number == 1
    
    def test_position_validation(self):
        """Test position mapping validation."""
        sql_code = "SELECT * FROM table1; INSERT INTO table2 VALUES (1);"
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        # Should have minimal validation errors for simple SQL
        assert len(mapper.validation_errors) == 0
        assert mapper.has_valid_position_mapping()
    
    def test_fuzzy_content_matching(self):
        """Test fuzzy content matching for error resolution."""
        sql_code = """
        SELECT col1, col2 FROM table1;
        INSERT INTO table2 VALUES (1, 'test');
        UPDATE table3 SET col1 = 'value' WHERE id = 1;
        """
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        # Test fuzzy matching
        candidates = mapper.find_statement_by_content_fuzzy("UPDATE table3", max_results=1)
        assert len(candidates) == 1
        assert candidates[0][0].statement_number == 3
        assert candidates[0][1] > 0.8  # High similarity
    
    def test_serialization(self):
        """Test serialization and deserialization."""
        sql_code = "SELECT * FROM table1; INSERT INTO table2 VALUES (1);"
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        # Serialize
        data = mapper.to_dict()
        assert 'statement_infos' in data
        assert 'original_sql' in data
        
        # Deserialize
        restored_mapper = EnhancedPositionMapper.from_dict(data)
        assert len(restored_mapper.statement_infos) == len(mapper.statement_infos)
        assert restored_mapper.original_sql == mapper.original_sql


class TestUniversalErrorExtractor:
    """Test cases for universal error extractor."""
    
    def test_postgresql_line_extraction(self):
        """Test PostgreSQL line number extraction."""
        error_message = "ERROR: syntax error at or near 'FROM' LINE 15: SELECT col1, col2 FRO table1;"
        
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)
        
        assert 'line' in position_info
        assert position_info['line'] == 15
    
    def test_position_extraction(self):
        """Test character position extraction."""
        error_message = "ERROR: syntax error at position 1234 in query"
        
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)
        
        assert 'position' in position_info
        assert position_info['position'] == 1234
    
    def test_oracle_line_extraction(self):
        """Test Oracle line number extraction."""
        error_message = "ORA-00933: SQL command not properly ended at line 25"
        
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)
        
        assert 'line' in position_info
        assert position_info['line'] == 25
    
    def test_no_position_info(self):
        """Test handling of error messages without position info."""
        error_message = "ERROR: table 'test_table' does not exist"
        
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)
        
        assert len(position_info) == 0


class TestSmartStatementResolver:
    """Test cases for smart statement resolver."""
    
    def test_position_based_resolution(self):
        """Test position-based statement resolution."""
        sql_code = """
        SELECT col1, col2
        FROM table1
        WHERE condition = 'value';
        
        INSERT INTO table2 (col1, col2)
        VALUES (1, 'test');
        """
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        resolver = SmartStatementResolver(mapper)
        
        # Test line-based resolution
        error_message = "ERROR: syntax error at or near 'FROM' LINE 3:"
        statement_num = resolver.resolve_statement_by_position(error_message)
        
        assert statement_num == 1  # Should resolve to first statement
    
    def test_content_based_resolution(self):
        """Test content-based statement resolution."""
        sql_code = """
        SELECT col1, col2 FROM table1;
        INSERT INTO table2 VALUES (1, 'test');
        UPDATE table3 SET col1 = 'value';
        """
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        resolver = SmartStatementResolver(mapper)
        
        # Test content-based resolution when position fails
        error_message = "ERROR: syntax error in UPDATE statement near 'SET col1'"
        statement_num = resolver.resolve_statement_by_position(error_message)
        
        assert statement_num == 3  # Should resolve to UPDATE statement
    
    def test_duplicate_handling(self):
        """Test handling of duplicate statements."""
        sql_code = """
        SELECT * FROM table1;
        SELECT * FROM table1;
        SELECT * FROM table1;
        """
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        resolver = SmartStatementResolver(mapper)
        
        # Test that different iterations select different duplicates
        error_message = "ERROR: syntax error at or near 'FROM' LINE 1:"
        
        stmt1 = resolver.resolve_statement_by_position(error_message, iteration_count=1)
        stmt2 = resolver.resolve_statement_by_position(error_message, iteration_count=2)
        stmt3 = resolver.resolve_statement_by_position(error_message, iteration_count=3)
        
        # Should cycle through the duplicates
        assert stmt1 != stmt2 or stmt2 != stmt3


class TestIntegration:
    """Integration tests for the complete enhanced system."""
    
    def test_end_to_end_error_mapping(self):
        """Test complete end-to-end error mapping scenario."""
        sql_code = """
        CREATE TABLE test_table (
            id INTEGER PRIMARY KEY,
            name VARCHAR(100)
        );
        
        INSERT INTO test_table VALUES (1, 'test');
        
        SELECT col1, col2
        FROM test_table
        WHERE id = 1;
        
        UPDATE test_table 
        SET name = 'updated'
        WHERE id = 1;
        """
        
        # Split with enhanced splitter
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        # Create resolver
        resolver = SmartStatementResolver(mapper)
        
        # Test error resolution for different types of errors
        test_cases = [
            ("ERROR: syntax error at or near 'FROM' LINE 8:", 3),  # SELECT statement
            ("ERROR: column 'col1' does not exist LINE 7:", 3),    # SELECT statement
            ("ERROR: syntax error in UPDATE statement", 4),        # UPDATE statement
        ]
        
        for error_message, expected_stmt in test_cases:
            resolved_stmt = resolver.resolve_statement_by_position(error_message)
            assert resolved_stmt == expected_stmt, f"Failed for error: {error_message}"
    
    def test_complex_postgresql_scenario(self):
        """Test complex PostgreSQL scenario with functions and procedures."""
        sql_code = """
        CREATE OR REPLACE FUNCTION calculate_total(p_id INTEGER)
        RETURNS DECIMAL AS
        $BODY$
        DECLARE
            v_total DECIMAL := 0;
        BEGIN
            SELECT SUM(amount) INTO v_total
            FROM transactions
            WHERE customer_id = p_id;
            
            RETURN COALESCE(v_total, 0);
        END;
        $BODY$
        LANGUAGE plpgsql;
        
        SELECT calculate_total(123) as total;
        """
        
        mapper = EnhancedPositionMapper()
        statements, mapper = mapper.split_with_comprehensive_mapping(sql_code)
        
        # Should properly merge the function definition
        assert len(statements) == 2
        
        # Function should be merged
        function_stmt = statements[0]
        assert 'CREATE OR REPLACE FUNCTION' in function_stmt
        assert '$BODY$' in function_stmt
        assert 'LANGUAGE plpgsql' in function_stmt
        
        # Test error resolution within the function
        resolver = SmartStatementResolver(mapper)
        error_message = "ERROR: syntax error in function body LINE 10:"
        resolved_stmt = resolver.resolve_statement_by_position(error_message)
        
        assert resolved_stmt == 1  # Should resolve to the function statement


if __name__ == "__main__":
    # Run basic tests
    test_splitter = TestEnhancedSQLSplitter()
    test_splitter.test_basic_splitting()
    test_splitter.test_postgresql_merging()
    test_splitter.test_as_is_splitting()
    test_splitter.test_comment_and_quote_handling()
    
    test_mapper = TestEnhancedPositionMapper()
    test_mapper.test_position_mapping_creation()
    test_mapper.test_position_validation()
    test_mapper.test_fuzzy_content_matching()
    test_mapper.test_serialization()
    
    test_extractor = TestUniversalErrorExtractor()
    test_extractor.test_postgresql_line_extraction()
    test_extractor.test_position_extraction()
    test_extractor.test_oracle_line_extraction()
    test_extractor.test_no_position_info()
    
    test_resolver = TestSmartStatementResolver()
    test_resolver.test_position_based_resolution()
    test_resolver.test_content_based_resolution()
    test_resolver.test_duplicate_handling()
    
    test_integration = TestIntegration()
    test_integration.test_end_to_end_error_mapping()
    test_integration.test_complex_postgresql_scenario()
    
    print("✅ All tests passed!")
